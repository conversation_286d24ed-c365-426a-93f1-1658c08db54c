import { LitElement, css, html } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { isAddress } from 'viem';

// Components
import '@shoelace-style/shoelace/dist/components/card/card.js';
import '@shoelace-style/shoelace/dist/components/button/button.js';
import '@shoelace-style/shoelace/dist/components/input/input.js';
import '@shoelace-style/shoelace/dist/components/divider/divider.js';
import type { SlInput } from '@shoelace-style/shoelace';
import '@components/common/transaction-watcher';
import type { TransactionWatcher } from '@components/common/transaction-watcher';

// Contracts
import { IdeaContract } from '@contracts/idea';

// Styles
import { changeCardStyles } from '@styles/change-card-styles';

@customElement('split-and-transfer')
export class SplitAndTransfer extends LitElement {
  static styles = [
    changeCardStyles,
    css`
      :host {
        display: block;
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
      }

      .form-container {
        margin-bottom: 2rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--main-foreground);
      }

      .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
      }

      .operation-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--main-foreground);
      }

      .description {
        color: var(--subtle-text);
        margin-bottom: 2rem;
        line-height: 1.5;
      }

      sl-input::part(base) {
        border-radius: var(--sl-input-border-radius-medium);
      }
    `,
  ];

  @property() entityId!: `0x${string}`;
  @property() position!: string;

  @query('form', true) form!: HTMLFormElement;
  @query('sl-input[name="numSplits"]', true) numSplitsInput!: SlInput;
  @query('sl-input[name="recipient"]', true) recipientInput!: SlInput;
  @query('transaction-watcher.split', true) splitTransaction!: TransactionWatcher;
  @query('transaction-watcher.transfer', true) transferTransaction!: TransactionWatcher;

  private get isTransferMode(): boolean {
    return window.location.pathname.includes('/transfer/');
  }

  private get isSplitMode(): boolean {
    return window.location.pathname.includes('/split/');
  }

  private handleFormSubmit = (event: Event) => {
    event.preventDefault();
    if (this.isSplitMode) {
      this.handleSplit();
    } else if (this.isTransferMode) {
      this.handleTransfer();
    }
  };

  private async handleSplit() {
    if (!this.form.checkValidity()) {
      this.form.reportValidity();
      return;
    }

    const numSplits = parseInt(this.numSplitsInput.value);
    if (numSplits < 2) {
      this.numSplitsInput.setCustomValidity('Number of splits must be at least 2');
      this.numSplitsInput.reportValidity();
      return;
    }

    this.splitTransaction.reset();
    try {
      const contract = new IdeaContract(this.entityId);
      this.splitTransaction.hash = await contract.write('split', [
        BigInt(this.position),
        BigInt(numSplits),
      ]);
    } catch (error) {
      console.error('Split transaction failed:', error);
      // The transaction watcher will handle the error display
    }
  }

  private async handleTransfer() {
    if (!this.form.checkValidity()) {
      this.form.reportValidity();
      return;
    }

    const recipient = this.recipientInput.value.trim();
    if (!isAddress(recipient)) {
      this.recipientInput.setCustomValidity('Please enter a valid Ethereum address');
      this.recipientInput.reportValidity();
      return;
    }

    this.transferTransaction.reset();
    try {
      const contract = new IdeaContract(this.entityId);
      this.transferTransaction.hash = await contract.write('transferPosition', [
        recipient as `0x${string}`,
        BigInt(this.position),
      ]);
    } catch (error) {
      console.error('Transfer transaction failed:', error);
      // The transaction watcher will handle the error display
    }
  }

  private handleTransactionSuccess = () => {
    // Navigate back or show success message
    window.history.back();
  };

  private handleInputChange = (event: Event) => {
    const input = event.target as SlInput;
    // Clear custom validity when user starts typing
    input.setCustomValidity('');
  };

  render() {
    return html`
      <sl-card>
        <div slot="header">
          <h1 class="operation-title">
            ${this.isSplitMode ? 'Split Position' : 'Transfer Position'}
          </h1>
        </div>

        <div class="description">
          ${this.isSplitMode
            ? html`
                Split your position into multiple smaller positions. This allows you to
                manage your stake more flexibly or transfer parts of it to different addresses.
              `
            : html`
                Transfer your position to another Ethereum address. The recipient will
                receive full ownership of this position.
              `}
        </div>

        <div class="form-container">
          <form @submit=${this.handleFormSubmit}>
            <div class="form-group">
              <label>Entity ID:</label>
              <sl-input
                value=${this.entityId}
                readonly
                help-text="The contract address of the idea or solution"
              ></sl-input>
            </div>

            <div class="form-group">
              <label>Position Index:</label>
              <sl-input
                value=${this.position.toString()}
                readonly
                help-text="The index of the position you want to ${this.isSplitMode ? 'split' : 'transfer'}"
              ></sl-input>
            </div>

            ${this.isSplitMode
              ? html`
                  <div class="form-group">
                    <label for="numSplits">Number of Splits:</label>
                    <sl-input
                      name="numSplits"
                      type="number"
                      min="2"
                      max="100"
                      required
                      placeholder="Enter number of splits (minimum 2)"
                      help-text="Your position will be divided into this many equal parts"
                      @sl-input=${this.handleInputChange}
                    ></sl-input>
                  </div>
                `
              : html`
                  <div class="form-group">
                    <label for="recipient">Recipient Address:</label>
                    <sl-input
                      name="recipient"
                      type="text"
                      required
                      placeholder="0x..."
                      help-text="The Ethereum address that will receive the position"
                      @sl-input=${this.handleInputChange}
                    ></sl-input>
                  </div>
                `}

            <div class="form-actions">
              <sl-button variant="default" @click=${() => window.history.back()}>
                Cancel
              </sl-button>
              <sl-button variant="primary" type="submit">
                ${this.isSplitMode ? 'Split Position' : 'Transfer Position'}
              </sl-button>
            </div>
          </form>
        </div>

        ${this.isSplitMode
          ? html`
              <transaction-watcher
                class="split"
                @transaction-success=${this.handleTransactionSuccess}
              >
                <div slot="pending">
                  <p>Splitting position... Please wait for the transaction to complete.</p>
                </div>
                <div slot="complete">
                  <p>Position split successfully! Your position has been divided into multiple parts.</p>
                </div>
                <div slot="error">
                  <p>Failed to split position. Please try again.</p>
                </div>
              </transaction-watcher>
            `
          : html`
              <transaction-watcher
                class="transfer"
                @transaction-success=${this.handleTransactionSuccess}
              >
                <div slot="pending">
                  <p>Transferring position... Please wait for the transaction to complete.</p>
                </div>
                <div slot="complete">
                  <p>Position transferred successfully! The recipient now owns this position.</p>
                </div>
                <div slot="error">
                  <p>Failed to transfer position. Please try again.</p>
                </div>
              </transaction-watcher>
            `}
      </sl-card>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'split-and-transfer': SplitAndTransfer;
  }
}
