import { LitElement, css, html } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { isAddress } from 'viem';

// Components
import '@shoelace-style/shoelace/dist/components/card/card.js';
import '@shoelace-style/shoelace/dist/components/button/button.js';
import '@shoelace-style/shoelace/dist/components/input/input.js';
import '@shoelace-style/shoelace/dist/components/divider/divider.js';
import type { SlInput } from '@shoelace-style/shoelace';
import '../common/components/transaction-watcher';
import type { TransactionWatcher } from '../common/components/transaction-watcher';

// Contracts
import { IdeaContract } from '@contracts/idea';

// Styles
import { changeCardStyles } from '@styles/change-card-styles';

@customElement('split-and-transfer')
export class SplitAndTransfer extends LitElement {
  static styles = [
    changeCardStyles,
    css`
      :host {
        display: block;
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
      }

      .form-container {
        margin-bottom: 2rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--main-foreground);
      }

      .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
      }

      .operation-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--main-foreground);
      }

      .description {
        color: var(--subtle-text);
        margin-bottom: 2rem;
        line-height: 1.5;
      }

      sl-input::part(base) {
        border-radius: var(--sl-input-border-radius-medium);
      }
    `,
  ];

  @property() entityId!: `0x${string}`;
  @property({ type: Number }) position!: number;

  @query('form', true) form!: HTMLFormElement;
  @query('sl-input[name="numSplits"]', true) numSplitsInput!: SlInput;
  @query('sl-input[name="recipient"]', true) recipientInput!: SlInput;
  @query('transaction-watcher.split', true) splitTransaction!: TransactionWatcher;
  @query('transaction-watcher.transfer', true) transferTransaction!: TransactionWatcher;

  private get isTransferMode(): boolean {
    return window.location.pathname.includes('/transfer/');
  }

  private get isSplitMode(): boolean {
    return window.location.pathname.includes('/split/');
  }

declare global {
  interface HTMLElementTagNameMap {
    'split-and-transfer': SplitAndTransfer;
  }
}
