import { LitElement, css, html, render } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('split-and-transfer')
export class SplitAndTransfer extends LitElement {
  static styles = css`
    :host {
    }
  `;

  @property() id: 0x${string}

  render() {
    return html`
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'split-and-transfer': SplitAndTransfer;
  }
}
